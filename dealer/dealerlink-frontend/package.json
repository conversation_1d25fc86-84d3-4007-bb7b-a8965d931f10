{"name": "dealerlink-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/inter": "^5.2.6", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.82.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-router-dom": "^7.6.3", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "tailwindcss": "^3.4.17", "vite": "^7.0.4"}}