import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout/Layout';
import { AuthProvider } from './contexts/AuthContext';

// Placeholder page components
const LoginPage = () => <div className="flex items-center justify-center h-screen bg-gray-50"><div className="bg-white p-8 rounded shadow w-full max-w-md text-center"><h1 className="text-2xl font-bold mb-4">Login Page</h1></div></div>;
const SignupPage = () => <div className="flex items-center justify-center h-screen bg-gray-50"><div className="bg-white p-8 rounded shadow w-full max-w-md text-center"><h1 className="text-2xl font-bold mb-4">Signup Page</h1></div></div>;
const DashboardPage = () => <div className="p-8">Dashboard</div>;
const InventoryPage = () => <div className="p-8">Inventory Management</div>;
const PricingPage = () => <div className="p-8">Pricing Engine</div>;
const TradeMarketplacePage = () => <div className="p-8">Trade Marketplace</div>;
const MessagesPage = () => <div className="p-8">Messages</div>;
const AnalyticsPage = () => <div className="p-8">Analytics</div>;
const SettingsPage = () => <div className="p-8">Settings</div>;
const TradeConfirmationPage = () => <div className="p-8">Trade Confirmation</div>;

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<SignupPage />} />
          <Route element={<Layout />}>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/inventory" element={<InventoryPage />} />
            <Route path="/pricing" element={<PricingPage />} />
            <Route path="/trade" element={<TradeMarketplacePage />} />
            <Route path="/messages" element={<MessagesPage />} />
            <Route path="/analytics" element={<AnalyticsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/trade/confirmation/:id" element={<TradeConfirmationPage />} />
          </Route>
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
