import React, { useState, useEffect } from 'react';
import {
  ArrowsRightLeftIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  MapPinIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { carsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const TradeMarketplacePage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('available');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLocation, setFilterLocation] = useState('all');
  const [trades, setTrades] = useState({
    available: [],
    pending: [],
    completed: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTrades = async () => {
      try {
        setLoading(true);
        // For now, we'll show a message that this feature is coming soon
        // In a real implementation, you would call a trades API endpoint
        setTrades({
          available: [],
          pending: [],
          completed: []
        });
      } catch (error) {
        console.error('Error fetching trades:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchTrades();
    }
  }, [user]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Trade Marketplace</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="card">
              <div className="skeleton h-48 w-full"></div>
              <div className="p-6 space-y-3">
                <div className="skeleton h-4 w-3/4"></div>
                <div className="skeleton h-4 w-1/2"></div>
                <div className="skeleton h-8 w-full"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Show coming soon message for now
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trade Marketplace</h1>
          <p className="text-gray-600">Connect with other dealers for vehicle trades</p>
        </div>
      </div>

      <div className="text-center py-12">
        <div className="card hover-lift">
          <div className="p-12">
            <div className="inline-flex items-center justify-center w-20 h-20 rounded-3xl mb-6 shadow-lg"
                 style={{ background: 'var(--gradient-primary)' }}>
              <ArrowsRightLeftIcon className="h-10 w-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Trade Marketplace Coming Soon!</h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              We're building an amazing trade marketplace where you can connect with other dealers
              to trade vehicles. This feature will be available soon!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-gradient">
                Get Notified When Available
              </button>
              <button className="btn-secondary">
                Learn More
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

};

export default TradeMarketplacePage;
        expires: '2024-02-20'
      },
      {
        id: 2,
        dealer: 'Premier Motors',
        location: 'Milwaukee, WI',
        distance: '85 miles',
        vehicle: {
          year: 2022,
          make: 'Ford',
          model: 'Escape',
          trim: 'SE',
          mileage: 18000,
          price: 29500,
          image: '/api/placeholder/300/200'
        },
        seeking: {
          makes: ['Toyota', 'Honda'],
          priceRange: '$27,000 - $32,000',
          maxMileage: 25000
        },
        postedDate: '2024-01-18',
        expires: '2024-02-18'
      }
    ],
    pending: [
      {
        id: 3,
        dealer: 'City Auto',
        location: 'Detroit, MI',
        distance: '280 miles',
        vehicle: {
          year: 2020,
          make: 'BMW',
          model: 'X3',
          trim: 'xDrive30i',
          mileage: 35000,
          price: 38000,
          image: '/api/placeholder/300/200'
        },
        status: 'pending_approval',
        requestDate: '2024-01-22'
      }
    ],
    completed: [
      {
        id: 4,
        dealer: 'Luxury Motors',
        location: 'Indianapolis, IN',
        distance: '185 miles',
        vehicle: {
          year: 2021,
          make: 'Audi',
          model: 'A4',
          trim: 'Premium',
          mileage: 28000,
          price: 35000,
          image: '/api/placeholder/300/200'
        },
        completedDate: '2024-01-15',
        rating: 5
      }
    ]
  };

  const tabs = [
    { id: 'available', name: 'Available Trades', count: trades.available.length },
    { id: 'pending', name: 'Pending', count: trades.pending.length },
    { id: 'completed', name: 'Completed', count: trades.completed.length }
  ];

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending_approval':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="h-3 w-3 mr-1" />
            Pending Approval
          </span>
        );
      default:
        return null;
    }
  };

  const renderTradeCard = (trade) => (
    <div key={trade.id} className="card card-hover group animate-fade-scale">
      <div className="relative overflow-hidden">
        <img
          src={trade.vehicle.image}
          alt={`${trade.vehicle.year} ${trade.vehicle.make} ${trade.vehicle.model}`}
          className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
        />
        <div className="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
          {trade.vehicle.year}
        </div>
      </div>
      
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {trade.vehicle.year} {trade.vehicle.make} {trade.vehicle.model} {trade.vehicle.trim}
            </h3>
            <p className="text-sm text-gray-600">{trade.dealer}</p>
            <div className="flex items-center text-sm text-gray-500 mt-1">
              <MapPinIcon className="h-4 w-4 mr-1" />
              {trade.location} • {trade.distance}
            </div>
          </div>
          {trade.status && getStatusBadge(trade.status)}
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
          <div>
            <span className="text-gray-500">Mileage:</span>
            <span className="ml-1 font-medium">{trade.vehicle.mileage.toLocaleString()}</span>
          </div>
          <div>
            <span className="text-gray-500">Price:</span>
            <span className="ml-1 font-medium text-primary-600">
              ${trade.vehicle.price.toLocaleString()}
            </span>
          </div>
        </div>

        {trade.seeking && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Seeking:</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p>Makes: {trade.seeking.makes.join(', ')}</p>
              <p>Price Range: {trade.seeking.priceRange}</p>
              <p>Max Mileage: {trade.seeking.maxMileage.toLocaleString()}</p>
            </div>
          </div>
        )}

        <div className="flex justify-between items-center">
          {activeTab === 'available' && (
            <>
              <span className="text-xs text-gray-500">
                Posted {new Date(trade.postedDate).toLocaleDateString()}
              </span>
              <button className="btn-primary text-sm">
                Request Trade
              </button>
            </>
          )}
          
          {activeTab === 'pending' && (
            <>
              <span className="text-xs text-gray-500">
                Requested {new Date(trade.requestDate).toLocaleDateString()}
              </span>
              <div className="flex space-x-2">
                <button className="text-sm px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200">
                  Cancel
                </button>
                <button className="text-sm px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200">
                  View Details
                </button>
              </div>
            </>
          )}
          
          {activeTab === 'completed' && (
            <>
              <span className="text-xs text-gray-500">
                Completed {new Date(trade.completedDate).toLocaleDateString()}
              </span>
              <div className="flex items-center">
                <div className="flex text-yellow-400">
                  {[...Array(trade.rating)].map((_, i) => (
                    <span key={i}>★</span>
                  ))}
                </div>
                <span className="ml-1 text-sm text-gray-600">({trade.rating}/5)</span>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Trade Marketplace</h1>
          <p className="text-gray-600">Connect with other dealers for vehicle trades</p>
        </div>
        <button className="btn-primary flex items-center">
          <ArrowsRightLeftIcon className="h-5 w-5 mr-2" />
          Post Trade Request
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.name}
              <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                {tab.count}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Filters */}
      {activeTab === 'available' && (
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Search by make, model, or dealer..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 input-field"
              />
            </div>
            
            <div className="relative">
              <FunnelIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
              <select
                value={filterLocation}
                onChange={(e) => setFilterLocation(e.target.value)}
                className="pl-10 input-field"
              >
                <option value="all">All Locations</option>
                <option value="50">Within 50 miles</option>
                <option value="100">Within 100 miles</option>
                <option value="200">Within 200 miles</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {trades[activeTab].length} trades found
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Trade Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {trades[activeTab].map(renderTradeCard)}
      </div>

      {/* Empty State */}
      {trades[activeTab].length === 0 && (
        <div className="text-center py-12">
          <ArrowsRightLeftIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No trades found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {activeTab === 'available' && "No available trades match your criteria."}
            {activeTab === 'pending' && "You don't have any pending trade requests."}
            {activeTab === 'completed' && "You haven't completed any trades yet."}
          </p>
        </div>
      )}
    </div>
  );
};

export default TradeMarketplacePage;
