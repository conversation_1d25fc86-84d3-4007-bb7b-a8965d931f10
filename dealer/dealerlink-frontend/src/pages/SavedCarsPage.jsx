import React, { useState, useEffect } from 'react';
import {
  HeartIcon,
  MagnifyingGlassIcon,
  MapPinIcon,
  ClockIcon,
  TrashIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { carsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const SavedCarsPage = () => {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [savedCars, setSavedCars] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSavedCars = async () => {
      try {
        setLoading(true);
        const response = await carsAPI.getSavedCars();
        setSavedCars(response.data.results || response.data);
      } catch (error) {
        console.error('Error fetching saved cars:', error);
        setSavedCars([]);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchSavedCars();
    }
  }, [user]);

  const filteredCars = savedCars.filter(car =>
    car.make?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.model?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.title?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUnsave = async (carId) => {
    try {
      await carsAPI.unsaveCar(carId);
      setSavedCars(savedCars.filter(car => car.id !== carId));
    } catch (error) {
      console.error('Error unsaving car:', error);
    }
  };

  const handleContactSeller = (car) => {
    // Navigate to messages or open contact modal
    console.log('Contacting seller for:', car);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Saved Cars</h1>
            <p className="text-gray-600">Loading your saved cars...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="card">
              <div className="skeleton h-48 w-full"></div>
              <div className="p-6 space-y-3">
                <div className="skeleton h-4 w-3/4"></div>
                <div className="skeleton h-4 w-1/2"></div>
                <div className="skeleton h-8 w-full"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Saved Cars</h1>
          <p className="text-gray-600">Cars you've saved for later</p>
        </div>
        <div className="flex items-center space-x-2">
          <HeartSolidIcon className="h-5 w-5 text-red-500" />
          <span className="text-sm text-gray-600 font-medium">
            {filteredCars.length} saved cars
          </span>
        </div>
      </div>

      {/* Search */}
      <div className="card animate-slide-up">
        <div className="p-6">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search your saved cars..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 input-field focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Saved Cars Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCars.map((car, index) => (
          <div 
            key={car.id} 
            className={`card card-hover group animate-fade-scale ${
              !car.isAvailable ? 'opacity-75' : ''
            }`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="relative overflow-hidden">
              <img
                src={car.image_url || car.images?.[0] || '/api/placeholder/300/200'}
                alt={`${car.year} ${car.make} ${car.model}`}
                className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
              />

              {/* Status badges */}
              <div className="absolute top-4 right-4 flex flex-col space-y-2">
                <div className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {car.year}
                </div>
                {car.status === 'sold' && (
                  <div className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Sold
                  </div>
                )}
                {car.price_change && car.price_change < 0 && (
                  <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Price Drop!
                  </div>
                )}
              </div>
              
              {/* Saved indicator */}
              <div className="absolute top-4 left-4">
                <HeartSolidIcon className="h-6 w-6 text-red-500" />
              </div>
            </div>
            
            <div className="p-6">
              <div className="mb-3">
                <h3 className={`text-lg font-semibold transition-colors duration-200 ${
                  car.status !== 'sold'
                    ? 'text-gray-900 group-hover:text-primary-600'
                    : 'text-gray-500'
                }`}>
                  {car.year} {car.make} {car.model}
                </h3>
                <div className="flex items-center space-x-2">
                  <span className={`text-2xl font-bold ${
                    car.status !== 'sold' ? 'text-primary-600' : 'text-gray-500'
                  }`}>
                    ${car.price?.toLocaleString()}
                  </span>
                  {car.price_change && car.price_change < 0 && (
                    <span className="text-sm text-green-600 font-medium">
                      (${Math.abs(car.price_change).toLocaleString()} less)
                    </span>
                  )}
                </div>
              </div>

              <div className="space-y-2 text-sm text-gray-600 mb-4">
                <div className="flex items-center justify-between">
                  <span>{car.mileage?.toLocaleString()} miles</span>
                  <div className="flex items-center">
                    <MapPinIcon className="h-4 w-4 mr-1" />
                    <span>{car.location || 'Location not specified'}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span>By {car.owner?.first_name || 'Seller'}</span>
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    <span>Saved {new Date(car.created_at || Date.now()).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2">
                {car.status !== 'sold' ? (
                  <button
                    onClick={() => handleContactSeller(car)}
                    className="flex-1 btn-gradient text-sm py-3 flex items-center justify-center"
                  >
                    <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                    Contact Seller
                  </button>
                ) : (
                  <div className="flex-1 bg-gray-100 text-gray-500 text-sm py-3 px-4 rounded-xl text-center">
                    No Longer Available
                  </div>
                )}

                <button
                  onClick={() => handleUnsave(car.id)}
                  className="p-3 border-2 border-gray-200 rounded-xl hover:bg-red-50 hover:border-red-300 hover:text-red-600 transition-all duration-300 group"
                  title="Remove from saved"
                >
                  <TrashIcon className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredCars.length === 0 && (
        <div className="text-center py-12">
          <HeartIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No saved cars found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm 
              ? "Try adjusting your search criteria." 
              : "Start browsing cars and save the ones you like!"
            }
          </p>
          {!searchTerm && (
            <button className="mt-4 btn-primary">
              Browse Cars
            </button>
          )}
        </div>
      )}

      {/* Price Alert Banner */}
      {filteredCars.some(car => car.price_change && car.price_change < 0) && (
        <div className="card hover-lift">
          <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-green-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-bold text-gray-900">Price Alerts</h3>
                <p className="text-sm text-gray-600">
                  Some of your saved cars have dropped in price! Check them out above.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SavedCarsPage;
