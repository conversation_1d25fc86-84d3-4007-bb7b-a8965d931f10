import React, { useState } from 'react';
import { 
  HeartIcon, 
  MagnifyingGlassIcon,
  MapPinIcon,
  ClockIcon,
  TrashIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

const SavedCarsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  
  // Mock saved cars data
  const savedCars = [
    {
      id: 1,
      make: 'Honda',
      model: 'Civic',
      year: 2022,
      price: 24500,
      mileage: 15000,
      location: 'Chicago, IL',
      distance: '2.3 miles away',
      image: '/api/placeholder/300/200',
      seller: '<PERSON>',
      savedDate: '2024-01-20',
      priceChange: null,
      isAvailable: true
    },
    {
      id: 2,
      make: 'Toyota',
      model: 'Camry',
      year: 2021,
      price: 25000,
      originalPrice: 26000,
      mileage: 22000,
      location: 'Milwaukee, WI',
      distance: '4.1 miles away',
      image: '/api/placeholder/300/200',
      seller: '<PERSON>',
      savedDate: '2024-01-18',
      priceChange: -1000,
      isAvailable: true
    },
    {
      id: 3,
      make: 'Ford',
      model: 'Mustang',
      year: 2023,
      price: 35000,
      mileage: 8000,
      location: 'Detroit, MI',
      distance: '6.8 miles away',
      image: '/api/placeholder/300/200',
      seller: 'Mike R.',
      savedDate: '2024-01-15',
      priceChange: null,
      isAvailable: false
    },
    {
      id: 4,
      make: 'BMW',
      model: 'X3',
      year: 2020,
      price: 38000,
      mileage: 35000,
      location: 'Indianapolis, IN',
      distance: '8.2 miles away',
      image: '/api/placeholder/300/200',
      seller: 'Lisa K.',
      savedDate: '2024-01-12',
      priceChange: null,
      isAvailable: true
    }
  ];

  const filteredCars = savedCars.filter(car =>
    car.make.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
    car.seller.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleUnsave = (carId) => {
    // Handle removing car from saved list
    console.log('Removing car from saved:', carId);
  };

  const handleContactSeller = (car) => {
    // Handle contacting seller
    console.log('Contacting seller for:', car);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Saved Cars</h1>
          <p className="text-gray-600">Cars you've saved for later</p>
        </div>
        <div className="flex items-center space-x-2">
          <HeartSolidIcon className="h-5 w-5 text-red-500" />
          <span className="text-sm text-gray-600 font-medium">
            {filteredCars.length} saved cars
          </span>
        </div>
      </div>

      {/* Search */}
      <div className="card animate-slide-up">
        <div className="p-6">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              placeholder="Search your saved cars..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 input-field focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Saved Cars Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCars.map((car, index) => (
          <div 
            key={car.id} 
            className={`card card-hover group animate-fade-scale ${
              !car.isAvailable ? 'opacity-75' : ''
            }`}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="relative overflow-hidden">
              <img
                src={car.image}
                alt={`${car.year} ${car.make} ${car.model}`}
                className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
              />
              
              {/* Status badges */}
              <div className="absolute top-4 right-4 flex flex-col space-y-2">
                <div className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {car.year}
                </div>
                {!car.isAvailable && (
                  <div className="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Sold
                  </div>
                )}
                {car.priceChange && (
                  <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Price Drop!
                  </div>
                )}
              </div>
              
              {/* Saved indicator */}
              <div className="absolute top-4 left-4">
                <HeartSolidIcon className="h-6 w-6 text-red-500" />
              </div>
            </div>
            
            <div className="p-6">
              <div className="mb-3">
                <h3 className={`text-lg font-semibold transition-colors duration-200 ${
                  car.isAvailable 
                    ? 'text-gray-900 group-hover:text-primary-600' 
                    : 'text-gray-500'
                }`}>
                  {car.year} {car.make} {car.model}
                </h3>
                <div className="flex items-center space-x-2">
                  <span className={`text-2xl font-bold ${
                    car.isAvailable ? 'text-primary-600' : 'text-gray-500'
                  }`}>
                    ${car.price.toLocaleString()}
                  </span>
                  {car.priceChange && (
                    <span className="text-sm text-green-600 font-medium">
                      (${Math.abs(car.priceChange).toLocaleString()} less)
                    </span>
                  )}
                </div>
              </div>
              
              <div className="space-y-2 text-sm text-gray-600 mb-4">
                <div className="flex items-center justify-between">
                  <span>{car.mileage.toLocaleString()} miles</span>
                  <div className="flex items-center">
                    <MapPinIcon className="h-4 w-4 mr-1" />
                    <span>{car.distance}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span>By {car.seller}</span>
                  <div className="flex items-center">
                    <ClockIcon className="h-4 w-4 mr-1" />
                    <span>Saved {new Date(car.savedDate).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-2">
                {car.isAvailable ? (
                  <button 
                    onClick={() => handleContactSeller(car)}
                    className="flex-1 btn-primary text-sm py-2 flex items-center justify-center"
                  >
                    <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                    Contact Seller
                  </button>
                ) : (
                  <div className="flex-1 bg-gray-100 text-gray-500 text-sm py-2 px-4 rounded-lg text-center">
                    No Longer Available
                  </div>
                )}
                
                <button 
                  onClick={() => handleUnsave(car.id)}
                  className="p-2 border border-gray-300 rounded-lg hover:bg-red-50 hover:border-red-300 hover:text-red-600 transition-colors duration-200"
                  title="Remove from saved"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredCars.length === 0 && (
        <div className="text-center py-12">
          <HeartIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No saved cars found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm 
              ? "Try adjusting your search criteria." 
              : "Start browsing cars and save the ones you like!"
            }
          </p>
          {!searchTerm && (
            <button className="mt-4 btn-primary">
              Browse Cars
            </button>
          )}
        </div>
      )}

      {/* Price Alert Banner */}
      {filteredCars.some(car => car.priceChange) && (
        <div className="card">
          <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
              </div>
              <div className="ml-4">
                <h3 className="text-sm font-medium text-gray-900">Price Alerts</h3>
                <p className="text-sm text-gray-600">
                  Some of your saved cars have dropped in price! Check them out above.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SavedCarsPage;
