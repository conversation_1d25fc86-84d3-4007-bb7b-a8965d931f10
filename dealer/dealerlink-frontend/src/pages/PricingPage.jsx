import React, { useState } from 'react';
import {
  CurrencyDollarIcon,
  SparklesIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

const PricingPage = () => {
  const [selectedVehicle, setSelectedVehicle] = useState('');
  const [pricingData, setPricingData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Mock vehicle data
  const vehicles = [
    { id: 1, name: '2022 Honda Accord LX', vin: '1HGCV1F30NA123456' },
    { id: 2, name: '2021 Toyota Camry LE', vin: '4T1C11AK5MU123456' },
    { id: 3, name: '2023 Ford F-150 XLT', vin: '1FTFW1E50NFA12345' },
    { id: 4, name: '2020 BMW X5 xDrive40i', vin: '5UXCR6C0XL9123456' }
  ];

  // Mock pricing analysis
  const mockPricingData = {
    currentPrice: 28500,
    suggestedPrice: 26800,
    marketAverage: 27200,
    confidence: 92,
    factors: [
      { name: 'Market Demand', impact: '+$800', positive: true },
      { name: 'Vehicle Condition', impact: '+$500', positive: true },
      { name: 'Mileage', impact: '-$1200', positive: false },
      { name: 'Local Competition', impact: '-$800', positive: false }
    ],
    marketTrends: [
      { period: 'Last 30 days', change: '+2.3%', positive: true },
      { period: 'Last 90 days', change: '-1.8%', positive: false },
      { period: 'Year over year', change: '+5.2%', positive: true }
    ],
    comparableVehicles: [
      { dealer: 'AutoMax', price: 27500, distance: '2.3 miles', daysOnLot: 15 },
      { dealer: 'Premier Motors', price: 26900, distance: '4.1 miles', daysOnLot: 22 },
      { dealer: 'City Auto', price: 28200, distance: '6.8 miles', daysOnLot: 8 }
    ]
  };

  const handleAnalyze = async () => {
    if (!selectedVehicle) return;
    
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setPricingData(mockPricingData);
      setLoading(false);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">AI Pricing Engine</h1>
        <p className="text-gray-600">Get intelligent pricing recommendations powered by market data</p>
      </div>

      {/* Vehicle Selection */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Select Vehicle for Analysis</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="vehicle" className="block text-sm font-medium text-gray-700 mb-2">
              Choose Vehicle
            </label>
            <select
              id="vehicle"
              value={selectedVehicle}
              onChange={(e) => setSelectedVehicle(e.target.value)}
              className="input-field"
            >
              <option value="">Select a vehicle...</option>
              {vehicles.map(vehicle => (
                <option key={vehicle.id} value={vehicle.id}>
                  {vehicle.name} - {vehicle.vin}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-end">
            <button
              onClick={handleAnalyze}
              disabled={!selectedVehicle || loading}
              className="btn-primary flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <SparklesIcon className="h-5 w-5 mr-2" />
              {loading ? 'Analyzing...' : 'Analyze Pricing'}
            </button>
          </div>
        </div>
      </div>

      {/* Pricing Analysis Results */}
      {pricingData && (
        <div className="space-y-6">
          {/* Price Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <CurrencyDollarIcon className="h-8 w-8 text-gray-400" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Current Price</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ${pricingData.currentPrice.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <SparklesIcon className="h-8 w-8 text-primary-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">AI Suggested Price</p>
                  <p className="text-2xl font-bold text-primary-600">
                    ${pricingData.suggestedPrice.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <ChartBarIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Market Average</p>
                  <p className="text-2xl font-bold text-blue-600">
                    ${pricingData.marketAverage.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Confidence Score */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Confidence Score</h3>
              <div className="flex items-center">
                <InformationCircleIcon className="h-5 w-5 text-gray-400 mr-1" />
                <span className="text-sm text-gray-500">Based on market data analysis</span>
              </div>
            </div>
            <div className="flex items-center">
              <div className="flex-1 bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-green-500 h-3 rounded-full" 
                  style={{ width: `${pricingData.confidence}%` }}
                ></div>
              </div>
              <span className="ml-4 text-lg font-semibold text-green-600">
                {pricingData.confidence}%
              </span>
            </div>
          </div>

          {/* Pricing Factors */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Pricing Factors</h3>
            <div className="space-y-3">
              {pricingData.factors.map((factor, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-700">{factor.name}</span>
                  <span className={`text-sm font-medium ${
                    factor.positive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {factor.impact}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Market Trends */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Market Trends</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {pricingData.marketTrends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-700">{trend.period}</span>
                  <div className="flex items-center">
                    {trend.positive ? (
                      <ArrowUpIcon className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <ArrowDownIcon className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm font-medium ${
                      trend.positive ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {trend.change}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Comparable Vehicles */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Comparable Vehicles</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dealer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Distance
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Days on Lot
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {pricingData.comparableVehicles.map((vehicle, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {vehicle.dealer}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${vehicle.price.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {vehicle.distance}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {vehicle.daysOnLot} days
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PricingPage;
