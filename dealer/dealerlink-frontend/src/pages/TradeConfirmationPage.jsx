import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  CheckCircleIcon, 
  DocumentTextIcon,
  TruckIcon,
  CalendarIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  PrinterIcon,
  ShareIcon
} from '@heroicons/react/24/outline';

const TradeConfirmationPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');

  // Mock trade confirmation data
  const tradeData = {
    id: id,
    status: 'completed',
    completedDate: '2024-01-25',
    tradePartner: {
      dealership: 'AutoMax Dealership',
      contact: '<PERSON>',
      phone: '(*************',
      email: '<EMAIL>',
      address: '456 Car Street, Chicago, IL 60601'
    },
    yourVehicle: {
      year: 2022,
      make: 'Honda',
      model: 'Accord',
      trim: 'LX',
      vin: '1HGCV1F30NA123456',
      mileage: 15000,
      value: 28500,
      image: '/api/placeholder/300/200'
    },
    receivedVehicle: {
      year: 2021,
      make: 'Toyota',
      model: 'Camry',
      trim: 'LE',
      vin: '4T1C11AK5MU123456',
      mileage: 22000,
      value: 26000,
      image: '/api/placeholder/300/200'
    },
    financials: {
      yourVehicleValue: 28500,
      receivedVehicleValue: 26000,
      cashDifference: 2500,
      fees: 150,
      totalAmount: 2650
    },
    documents: [
      { name: 'Trade Agreement', type: 'PDF', size: '245 KB' },
      { name: 'Title Transfer - Honda Accord', type: 'PDF', size: '189 KB' },
      { name: 'Title Transfer - Toyota Camry', type: 'PDF', size: '192 KB' },
      { name: 'Inspection Report', type: 'PDF', size: '156 KB' }
    ]
  };

  const handleRatingSubmit = () => {
    // Handle rating submission
    console.log('Rating submitted:', rating, feedback);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center">
          <CheckCircleIcon className="h-8 w-8 text-green-600 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-green-900">Trade Completed Successfully!</h1>
            <p className="text-green-700">
              Trade #{tradeData.id} was completed on {new Date(tradeData.completedDate).toLocaleDateString()}
            </p>
          </div>
        </div>
      </div>

      {/* Trade Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Your Vehicle */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Vehicle You Traded</h2>
          <div className="flex items-start space-x-4">
            <img
              src={tradeData.yourVehicle.image}
              alt={`${tradeData.yourVehicle.year} ${tradeData.yourVehicle.make} ${tradeData.yourVehicle.model}`}
              className="w-24 h-16 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">
                {tradeData.yourVehicle.year} {tradeData.yourVehicle.make} {tradeData.yourVehicle.model} {tradeData.yourVehicle.trim}
              </h3>
              <p className="text-sm text-gray-600">VIN: {tradeData.yourVehicle.vin}</p>
              <p className="text-sm text-gray-600">Mileage: {tradeData.yourVehicle.mileage.toLocaleString()}</p>
              <p className="text-lg font-semibold text-primary-600 mt-2">
                ${tradeData.yourVehicle.value.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        {/* Received Vehicle */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Vehicle You Received</h2>
          <div className="flex items-start space-x-4">
            <img
              src={tradeData.receivedVehicle.image}
              alt={`${tradeData.receivedVehicle.year} ${tradeData.receivedVehicle.make} ${tradeData.receivedVehicle.model}`}
              className="w-24 h-16 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">
                {tradeData.receivedVehicle.year} {tradeData.receivedVehicle.make} {tradeData.receivedVehicle.model} {tradeData.receivedVehicle.trim}
              </h3>
              <p className="text-sm text-gray-600">VIN: {tradeData.receivedVehicle.vin}</p>
              <p className="text-sm text-gray-600">Mileage: {tradeData.receivedVehicle.mileage.toLocaleString()}</p>
              <p className="text-lg font-semibold text-primary-600 mt-2">
                ${tradeData.receivedVehicle.value.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Financial Summary */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Financial Summary</h2>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Your Vehicle Value:</span>
            <span className="font-medium">${tradeData.financials.yourVehicleValue.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Received Vehicle Value:</span>
            <span className="font-medium">${tradeData.financials.receivedVehicleValue.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Processing Fees:</span>
            <span className="font-medium">${tradeData.financials.fees.toLocaleString()}</span>
          </div>
          <div className="border-t pt-3 flex justify-between">
            <span className="font-medium text-gray-900">Cash Difference (You Received):</span>
            <span className="font-bold text-green-600">${tradeData.financials.totalAmount.toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Trade Partner Info */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Trade Partner Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900">{tradeData.tradePartner.dealership}</h3>
            <p className="text-gray-600">{tradeData.tradePartner.contact}</p>
            <div className="mt-3 space-y-2">
              <div className="flex items-center text-sm text-gray-600">
                <PhoneIcon className="h-4 w-4 mr-2" />
                {tradeData.tradePartner.phone}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <EnvelopeIcon className="h-4 w-4 mr-2" />
                {tradeData.tradePartner.email}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <MapPinIcon className="h-4 w-4 mr-2" />
                {tradeData.tradePartner.address}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Documents */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900">Trade Documents</h2>
          <div className="flex space-x-2">
            <button className="btn-secondary flex items-center">
              <PrinterIcon className="h-4 w-4 mr-2" />
              Print All
            </button>
            <button className="btn-secondary flex items-center">
              <ShareIcon className="h-4 w-4 mr-2" />
              Share
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {tradeData.documents.map((doc, index) => (
            <div key={index} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
              <DocumentTextIcon className="h-8 w-8 text-gray-400 mr-3" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                <p className="text-xs text-gray-500">{doc.type} • {doc.size}</p>
              </div>
              <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                Download
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Rating & Feedback */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Rate Your Experience</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              How was your trade experience with {tradeData.tradePartner.dealership}?
            </label>
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  className={`text-2xl ${
                    star <= rating ? 'text-yellow-400' : 'text-gray-300'
                  } hover:text-yellow-400`}
                >
                  ★
                </button>
              ))}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Feedback (Optional)
            </label>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              rows={3}
              className="input-field"
              placeholder="Share your experience with other dealers..."
            />
          </div>
          
          <button
            onClick={handleRatingSubmit}
            disabled={rating === 0}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Submit Rating
          </button>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between">
        <button
          onClick={() => navigate('/trade')}
          className="btn-secondary"
        >
          Back to Trade Marketplace
        </button>
        <button
          onClick={() => navigate('/dashboard')}
          className="btn-primary"
        >
          Return to Dashboard
        </button>
      </div>
    </div>
  );
};

export default TradeConfirmationPage;
