import React from 'react';
import { Link } from 'react-router-dom';
import { 
  ArrowRightIcon, 
  StarIcon, 
  ShieldCheckIcon, 
  TrophyIcon, 
  UsersIcon,
  SparklesIcon,
  ChartBarIcon,
  ArrowsRightLeftIcon
} from '@heroicons/react/24/outline';
import AnimatedCar from '../components/AnimatedCar';

const LandingPage = () => {
  const features = [
    {
      icon: <SparklesIcon className="h-8 w-8" />,
      title: "Smart Pricing",
      description: "Get fair market value suggestions based on real-time data to price your car competitively."
    },
    {
      icon: <UsersIcon className="h-8 w-8" />,
      title: "Local Community",
      description: "Connect with car buyers and sellers in your area for easy, convenient transactions."
    },
    {
      icon: <ArrowsRightLeftIcon className="h-8 w-8" />,
      title: "Easy Trading",
      description: "Trade your current car for something new with other community members."
    },
    {
      icon: <ShieldCheckIcon className="h-8 w-8" />,
      title: "Safe & Secure",
      description: "Verified profiles and secure messaging to ensure safe transactions for everyone."
    }
  ];

  const stats = [
    { number: "25K+", label: "Active Users" },
    { number: "50K+", label: "Cars Listed" },
    { number: "98%", label: "Successful Deals" },
    { number: "24/7", label: "Community Support" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative hero-gradient overflow-hidden">
        <div className="absolute inset-0">
          <div className="particle particle-1"></div>
          <div className="particle particle-2"></div>
          <div className="particle particle-3"></div>
          <div className="particle particle-4"></div>
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="text-white animate-slide-up">
              <h1 className="text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Buy, Sell & Trade
                <span className="block text-primary-200">Cars Easily</span>
              </h1>
              <p className="text-xl mb-8 text-primary-100 leading-relaxed">
                Connect with car enthusiasts, find your dream car, sell your current ride,
                or trade with others in your local community.
              </p>
              <div className="flex flex-col sm:flex-row gap-6">
                <Link
                  to="/register"
                  className="inline-flex items-center px-10 py-5 bg-white text-gray-900 rounded-2xl font-bold hover:bg-gray-100 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105 group"
                >
                  Join CarConnect
                  <ArrowRightIcon className="ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform duration-300" />
                </Link>
                <Link
                  to="/login"
                  className="inline-flex items-center px-10 py-5 glass text-white rounded-2xl font-bold hover:bg-white/30 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105"
                >
                  Sign In
                </Link>
              </div>
            </div>
            <div className="relative animate-fade-scale">
              <AnimatedCar />
            </div>
          </div>
        </div>
        
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary-400/20 rounded-full animate-bounce"></div>
        <div className="absolute bottom-20 right-10 w-16 h-16 bg-primary-300/20 rounded-full animate-pulse"></div>
        <div className="absolute top-1/2 left-1/4 w-12 h-12 bg-white/10 rounded-full animate-ping"></div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div 
                key={stat.label} 
                className="text-center animate-fade-scale"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="text-4xl font-bold text-primary-600 mb-2">{stat.number}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-slide-up">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose CarConnect?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The easiest way to buy, sell, and trade cars with people in your community.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={feature.title}
                className="card hover-lift group animate-fade-scale"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="p-8 text-center">
                  <div className="inline-flex items-center justify-center w-20 h-20 rounded-3xl mb-6 group-hover:scale-110 transition-all duration-300 shadow-lg"
                       style={{
                         background: index % 2 === 0 ? 'var(--gradient-primary)' : 'var(--gradient-secondary)'
                       }}>
                    <div className="text-white text-2xl">
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 hero-uber">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <div className="animate-slide-up">
            <h2 className="text-5xl font-bold text-white mb-8">Ready to Find Your Perfect Car?</h2>
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed">
              Join thousands of car enthusiasts who are buying, selling, and trading on CarConnect.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/register"
                className="inline-flex items-center px-12 py-6 bg-white text-gray-900 rounded-2xl font-bold hover:bg-gray-100 transition-all duration-300 shadow-2xl hover:shadow-3xl hover:scale-105 group"
              >
                Get Started Today
                <ArrowRightIcon className="ml-3 h-6 w-6 group-hover:translate-x-2 transition-transform duration-300" />
              </Link>
              <Link
                to="/dashboard"
                className="inline-flex items-center px-12 py-6 glass-dark text-white rounded-2xl font-bold hover:bg-white/20 transition-all duration-300 shadow-2xl hover:shadow-3xl hover:scale-105"
              >
                View Demo
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;
