import React, { useState, useEffect } from 'react';
import {
  TruckIcon,
  CurrencyDollarIcon,
  ArrowsRightLeftIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  SparklesIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import AnimatedCar from '../components/AnimatedCar';
import { carsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

const DashboardPage = () => {
  const { user } = useAuth();
  const [featuredCars, setFeaturedCars] = useState([]);
  const [myListings, setMyListings] = useState([]);
  const [savedCars, setSavedCars] = useState([]);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch featured cars
        const carsResponse = await carsAPI.getCars({ limit: 6 });
        setFeaturedCars(carsResponse.data.results || carsResponse.data);

        if (user) {
          // Fetch user's listings
          const listingsResponse = await carsAPI.getMyListings();
          setMyListings(listingsResponse.data.results || listingsResponse.data);

          // Fetch saved cars
          const savedResponse = await carsAPI.getSavedCars();
          setSavedCars(savedResponse.data.results || savedResponse.data);

          // Fetch messages
          const messagesResponse = await carsAPI.getMessages();
          setMessages(messagesResponse.data.results || messagesResponse.data);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  const handleSaveCar = async (carId) => {
    try {
      const car = featuredCars.find(c => c.id === carId);
      if (car?.is_saved) {
        await carsAPI.unsaveCar(carId);
      } else {
        await carsAPI.saveCar(carId);
      }

      // Update the car's saved status
      setFeaturedCars(prev =>
        prev.map(c =>
          c.id === carId ? { ...c, is_saved: !c.is_saved } : c
        )
      );
    } catch (error) {
      console.error('Error saving/unsaving car:', error);
    }
  };

  const stats = [
    {
      name: 'Cars Available',
      value: '2,847', // This would come from a total count API endpoint
      change: '+12%',
      changeType: 'increase',
      icon: TruckIcon,
    },
    {
      name: 'Your Listings',
      value: myListings.length.toString(),
      change: '+1',
      changeType: 'increase',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'Messages',
      value: messages.length.toString(),
      change: '+4',
      changeType: 'increase',
      icon: ArrowsRightLeftIcon,
    },
    {
      name: 'Saved Cars',
      value: savedCars.length.toString(),
      change: '+2',
      changeType: 'increase',
      icon: HeartIcon,
    },
  ];

  // Generate recent activity based on user's actual data
  const recentActivity = [
    ...(myListings.length > 0 ? [{
      id: 1,
      type: 'listing',
      description: `Your ${myListings[0]?.year} ${myListings[0]?.make} ${myListings[0]?.model} listing is active`,
      time: 'Today',
      amount: `${myListings[0]?.views || 0} views`
    }] : []),
    ...(savedCars.length > 0 ? [{
      id: 2,
      type: 'saved',
      description: `You saved ${savedCars.length} car${savedCars.length > 1 ? 's' : ''}`,
      time: 'Recently',
      amount: `${savedCars.length} saved`
    }] : []),
    ...(messages.length > 0 ? [{
      id: 3,
      type: 'message',
      description: `You have ${messages.length} message${messages.length > 1 ? 's' : ''}`,
      time: 'Recent',
      amount: `${messages.length} messages`
    }] : []),
    {
      id: 4,
      type: 'welcome',
      description: `Welcome to CarConnect, ${user?.first_name}! Start exploring cars in your area.`,
      time: 'Just now',
      amount: 'New user'
    }
  ].slice(0, 4);

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="relative hero-gradient rounded-3xl overflow-hidden shadow-xl-custom">
        <div className="absolute inset-0">
          <div className="particle particle-1"></div>
          <div className="particle particle-2"></div>
          <div className="particle particle-3"></div>
        </div>
        <div className="relative px-8 py-12">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div className="text-white animate-slide-up">
              <h1 className="text-4xl lg:text-5xl font-bold leading-tight mb-4">
                Welcome back,
                <span className="block text-primary-200">{user?.first_name}!</span>
              </h1>
              <p className="text-xl mb-6 text-primary-100 leading-relaxed">
                Discover amazing cars, connect with sellers, and find your perfect ride in the community marketplace.
              </p>
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <SparklesIcon className="h-6 w-6 text-yellow-400 mr-2" />
                  <span className="text-primary-100">Smart Car Matching</span>
                </div>
              </div>
            </div>
            <div className="relative animate-fade-scale">
              <AnimatedCar />
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute top-8 left-8 w-16 h-16 bg-primary-400/20 rounded-full animate-bounce"></div>
        <div className="absolute bottom-8 right-8 w-12 h-12 bg-primary-300/20 rounded-full animate-pulse"></div>
        <div className="absolute top-1/2 left-1/4 w-8 h-8 bg-white/10 rounded-full animate-ping"></div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div
            key={stat.name}
            className="card hover-lift group"
            style={{
              animationDelay: `${index * 0.1}s`,
              background: index % 2 === 0 ? 'var(--gradient-primary)' : 'var(--gradient-secondary)'
            }}
          >
            <div className="p-8 text-white">
              <div className="flex items-center justify-between mb-4">
                <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
                  <stat.icon className="h-8 w-8 text-white" />
                </div>
                <div className={`flex items-center text-sm font-semibold px-3 py-1 rounded-full ${
                  stat.changeType === 'increase' ? 'bg-green-400/20 text-green-100' : 'bg-red-400/20 text-red-100'
                }`}>
                  {stat.changeType === 'increase' ? (
                    <ArrowUpIcon className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDownIcon className="h-4 w-4 mr-1" />
                  )}
                  <span>{stat.change}</span>
                </div>
              </div>
              <div>
                <p className="text-3xl font-bold mb-2">
                  {stat.value}
                </p>
                <p className="text-white/80 font-medium">
                  {stat.name}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Featured Cars */}
      <div className="card">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Featured Cars Near You</h3>
            <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              View All →
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="card animate-pulse">
                  <div className="bg-gray-300 h-48 w-full"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                    <div className="h-6 bg-gray-300 rounded w-1/2"></div>
                    <div className="h-4 bg-gray-300 rounded w-full"></div>
                  </div>
                </div>
              ))
            ) : (
              featuredCars.slice(0, 3).map((car, index) => (
              <div
                key={car.id}
                className="card hover-lift group animate-fade-scale"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="relative overflow-hidden">
                  <img
                    src={car.images?.[0]?.image || '/api/placeholder/300/200'}
                    alt={`${car.year} ${car.make} ${car.model}`}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {car.year}
                  </div>
                </div>

                <div className="p-4">
                  <div className="mb-3">
                    <h4 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                      {car.year} {car.make} {car.model}
                    </h4>
                    <p className="text-2xl font-bold text-primary-600">${car.price.toLocaleString()}</p>
                  </div>

                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    <div className="flex items-center justify-between">
                      <span>{car.mileage?.toLocaleString()} miles</span>
                      <span>{car.distance || '2.3 miles away'}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>By {car.seller?.first_name} {car.seller?.last_name?.[0]}.</span>
                      <span>{new Date(car.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <button className="flex-1 btn-gradient text-sm py-3 px-4 rounded-xl font-semibold">
                      Contact Seller
                    </button>
                    <button
                      className="p-3 border-2 border-gray-200 rounded-xl hover:bg-red-50 hover:border-red-300 hover:text-red-600 transition-all duration-300 group"
                      onClick={() => handleSaveCar(car.id)}
                    >
                      <span className="text-lg group-hover:scale-125 transition-transform duration-300">
                        {car.is_saved ? '♥' : '♡'}
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            ))
            )}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Your Activity</h3>
            <div className="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              Live
            </div>
          </div>
        </div>
        <div className="divide-y divide-gray-200">
          {recentActivity.map((activity, index) => (
            <div
              key={activity.id}
              className="px-6 py-4 hover:bg-gray-50 transition-colors duration-200"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.type === 'message' ? 'bg-green-400' :
                    activity.type === 'trade' ? 'bg-blue-400' :
                    activity.type === 'saved' ? 'bg-red-400' :
                    activity.type === 'listing' ? 'bg-purple-400' :
                    activity.type === 'welcome' ? 'bg-primary-400' : 'bg-yellow-400'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
                <div className="text-sm font-medium text-primary-600">{activity.amount}</div>
              </div>
            </div>
          ))}
        </div>
        <div className="px-6 py-3 bg-gradient-to-r from-gray-50 to-primary-50 text-center">
          <button className="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200">
            View all activity →
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
