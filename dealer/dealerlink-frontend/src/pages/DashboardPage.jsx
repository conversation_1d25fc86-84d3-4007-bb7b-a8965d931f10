import React from 'react';
import {
  TruckIcon,
  CurrencyDollarIcon,
  ArrowsRightLeftIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import AnimatedCar from '../components/AnimatedCar';

const DashboardPage = () => {
  // Mock data for charts
  const salesData = [
    { month: 'Jan', sales: 45, revenue: 450000 },
    { month: 'Feb', sales: 52, revenue: 520000 },
    { month: 'Mar', sales: 48, revenue: 480000 },
    { month: 'Apr', sales: 61, revenue: 610000 },
    { month: 'May', sales: 55, revenue: 550000 },
    { month: 'Jun', sales: 67, revenue: 670000 },
  ];

  const inventoryData = [
    { category: 'Sedans', count: 45 },
    { category: 'SUVs', count: 32 },
    { category: 'Trucks', count: 28 },
    { category: 'Coupes', count: 15 },
    { category: 'Convertibles', count: 8 },
  ];

  const stats = [
    {
      name: 'Cars Available',
      value: '2,847',
      change: '+12%',
      changeType: 'increase',
      icon: TruckIcon,
    },
    {
      name: 'Your Listings',
      value: '3',
      change: '+1',
      changeType: 'increase',
      icon: CurrencyDollarIcon,
    },
    {
      name: 'Messages',
      value: '8',
      change: '+4',
      changeType: 'increase',
      icon: ArrowsRightLeftIcon,
    },
    {
      name: 'Saved Cars',
      value: '12',
      change: '+2',
      changeType: 'increase',
      icon: ChartBarIcon,
    },
  ];

  const recentActivity = [
    { id: 1, type: 'message', description: 'New message about your 2022 Honda Civic', time: '2 hours ago', amount: 'Sarah M.' },
    { id: 2, type: 'saved', description: 'Someone saved your 2021 Toyota Camry listing', time: '4 hours ago', amount: 'Mike R.' },
    { id: 3, type: 'view', description: 'Your Ford Mustang listing was viewed 12 times', time: '6 hours ago', amount: '12 views' },
    { id: 4, type: 'trade', description: 'Trade offer received for your BMW X3', time: '1 day ago', amount: 'John D.' },
  ];

  return (
    <div className="space-y-6">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 rounded-2xl overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative px-8 py-12">
          <div className="grid lg:grid-cols-2 gap-8 items-center">
            <div className="text-white animate-slide-up">
              <h1 className="text-4xl lg:text-5xl font-bold leading-tight mb-4">
                Welcome to
                <span className="block text-primary-200">CarConnect</span>
              </h1>
              <p className="text-xl mb-6 text-primary-100 leading-relaxed">
                Discover amazing cars, connect with sellers, and find your perfect ride in the community marketplace.
              </p>
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <SparklesIcon className="h-6 w-6 text-yellow-400 mr-2" />
                  <span className="text-primary-100">Smart Car Matching</span>
                </div>
              </div>
            </div>
            <div className="relative animate-fade-scale">
              <AnimatedCar />
            </div>
          </div>
        </div>

        {/* Animated background elements */}
        <div className="absolute top-8 left-8 w-16 h-16 bg-primary-400/20 rounded-full animate-bounce"></div>
        <div className="absolute bottom-8 right-8 w-12 h-12 bg-primary-300/20 rounded-full animate-pulse"></div>
        <div className="absolute top-1/2 left-1/4 w-8 h-8 bg-white/10 rounded-full animate-ping"></div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div
            key={stat.name}
            className="card card-hover group"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary-100 w-12 h-12 rounded-lg flex items-center justify-center group-hover:bg-primary-600 group-hover:text-white transition-all duration-300">
                    <stat.icon className="h-6 w-6 text-primary-600 group-hover:text-white" />
                  </div>
                </div>
                <div className="ml-4 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{stat.name}</dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">{stat.value}</div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.changeType === 'increase' ? (
                          <ArrowUpIcon className="h-4 w-4 flex-shrink-0 self-center" />
                        ) : (
                          <ArrowDownIcon className="h-4 w-4 flex-shrink-0 self-center" />
                        )}
                        <span className="ml-1">{stat.change}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Featured Cars */}
      <div className="card">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900">Featured Cars Near You</h3>
            <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              View All →
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                id: 1,
                make: 'Honda',
                model: 'Civic',
                year: 2022,
                price: 24500,
                mileage: 15000,
                location: '2.3 miles away',
                image: '/api/placeholder/300/200',
                seller: 'John D.',
                posted: '2 days ago'
              },
              {
                id: 2,
                make: 'Toyota',
                model: 'Camry',
                year: 2021,
                price: 26000,
                mileage: 22000,
                location: '4.1 miles away',
                image: '/api/placeholder/300/200',
                seller: 'Sarah M.',
                posted: '1 week ago'
              },
              {
                id: 3,
                make: 'Ford',
                model: 'Mustang',
                year: 2023,
                price: 35000,
                mileage: 8000,
                location: '6.8 miles away',
                image: '/api/placeholder/300/200',
                seller: 'Mike R.',
                posted: '3 days ago'
              }
            ].map((car, index) => (
              <div
                key={car.id}
                className="card card-hover group animate-fade-scale"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="relative overflow-hidden">
                  <img
                    src={car.image}
                    alt={`${car.year} ${car.make} ${car.model}`}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 right-4 bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    {car.year}
                  </div>
                </div>

                <div className="p-4">
                  <div className="mb-3">
                    <h4 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                      {car.year} {car.make} {car.model}
                    </h4>
                    <p className="text-2xl font-bold text-primary-600">${car.price.toLocaleString()}</p>
                  </div>

                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    <div className="flex items-center justify-between">
                      <span>{car.mileage.toLocaleString()} miles</span>
                      <span>{car.location}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>By {car.seller}</span>
                      <span>{car.posted}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button className="flex-1 btn-primary text-sm py-2">
                      Contact Seller
                    </button>
                    <button className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                      ♡
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Your Activity</h3>
            <div className="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              Live
            </div>
          </div>
        </div>
        <div className="divide-y divide-gray-200">
          {recentActivity.map((activity, index) => (
            <div
              key={activity.id}
              className="px-6 py-4 hover:bg-gray-50 transition-colors duration-200"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    activity.type === 'message' ? 'bg-green-400' :
                    activity.type === 'trade' ? 'bg-blue-400' :
                    activity.type === 'saved' ? 'bg-red-400' : 'bg-yellow-400'
                  }`}></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
                <div className="text-sm font-medium text-primary-600">{activity.amount}</div>
              </div>
            </div>
          ))}
        </div>
        <div className="px-6 py-3 bg-gradient-to-r from-gray-50 to-primary-50 text-center">
          <button className="text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200">
            View all activity →
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
