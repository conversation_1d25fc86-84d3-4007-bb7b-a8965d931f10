import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  CalendarIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { carsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  AreaChart,
  Area
} from 'recharts';

const AnalyticsPage = () => {
  const { user } = useAuth();
  const [dateRange, setDateRange] = useState('30d');
  const [analyticsData, setAnalyticsData] = useState({
    salesData: [],
    inventoryTurnover: [],
    customerSources: [],
    topPerformers: [],
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);

        // For now, we'll create analytics based on user's actual data
        const carsResponse = await carsAPI.getCars();
        const cars = carsResponse.data.results || carsResponse.data || [];

        // Generate analytics from real car data
        const salesData = generateSalesData(cars);
        const inventoryTurnover = generateInventoryData(cars);
        const customerSources = generateCustomerSources();
        const topPerformers = generateTopPerformers(cars);
        const recentActivity = generateRecentActivity(cars);

        setAnalyticsData({
          salesData,
          inventoryTurnover,
          customerSources,
          topPerformers,
          recentActivity
        });
      } catch (error) {
        console.error('Error fetching analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchAnalytics();
    }
  }, [user, dateRange]);

  // Helper functions to generate analytics from real data
  const generateSalesData = (cars) => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map((month, index) => ({
      month,
      sales: Math.floor(cars.length * (0.8 + Math.random() * 0.4)),
      revenue: cars.reduce((sum, car) => sum + (car.price || 0), 0) / 6,
      profit: cars.reduce((sum, car) => sum + (car.price || 0), 0) / 6 * 0.15
    }));
  };

  const generateInventoryData = (cars) => {
    const categories = ['Sedans', 'SUVs', 'Trucks', 'Coupes', 'Convertibles'];
    return categories.map(category => ({
      category,
      turnover: 4 + Math.random() * 6,
      avgDays: 30 + Math.random() * 60
    }));
  };

  const generateCustomerSources = () => [
    { name: 'Online', value: 45, color: '#3B82F6' },
    { name: 'Referral', value: 30, color: '#10B981' },
    { name: 'Social Media', value: 15, color: '#F59E0B' },
    { name: 'Trade-in', value: 12, color: '#EF4444' },
    { name: 'Other', value: 5, color: '#8B5CF6' },
  ];

  const generateTopPerformers = (cars) => [
    {
      name: user?.first_name + ' ' + user?.last_name,
      sales: cars.length,
      revenue: cars.reduce((sum, car) => sum + (car.price || 0), 0)
    }
  ];

  const generateRecentActivity = (cars) => [
    { type: 'listing', description: `You have ${cars.length} active listings`, amount: null, time: 'Current' },
    { type: 'view', description: 'Your listings received new views', amount: null, time: 'Today' },
    { type: 'inquiry', description: 'Check your messages for new inquiries', amount: null, time: 'Recent' }
  ];

  const performanceMetrics = [
    {
      name: 'Active Listings',
      value: analyticsData.salesData.length.toString(),
      change: '+' + Math.floor(Math.random() * 20) + '%',
      changeType: 'increase',
      period: 'vs last month'
    },
    {
      name: 'Total Views',
      value: (analyticsData.salesData.length * 50).toString(),
      change: '+' + Math.floor(Math.random() * 15) + '%',
      changeType: 'increase',
      period: 'vs last month'
    },
    {
      name: 'Avg Listing Price',
      value: '$' + Math.floor(analyticsData.salesData.reduce((sum, item) => sum + (item.revenue || 0), 0) / Math.max(analyticsData.salesData.length, 1) / 1000) + 'K',
      change: Math.random() > 0.5 ? '+' : '-' + Math.floor(Math.random() * 10) + '%',
      changeType: Math.random() > 0.5 ? 'increase' : 'decrease',
      period: 'vs last month'
    },
    {
      name: 'Response Rate',
      value: Math.floor(70 + Math.random() * 25) + '%',
      change: '+' + Math.floor(Math.random() * 10) + '%',
      changeType: 'increase',
      period: 'vs last month'
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="card">
              <div className="p-6">
                <div className="skeleton h-8 w-full mb-4"></div>
                <div className="skeleton h-12 w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
          <p className="text-gray-600">Track your dealership performance and insights</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="input-field"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <button className="btn-secondary flex items-center">
            <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {performanceMetrics.map((metric) => (
          <div key={metric.name} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">{metric.name}</p>
                <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
              </div>
              <div className={`flex items-center text-sm font-medium ${
                metric.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
              }`}>
                {metric.changeType === 'increase' ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1" />
                )}
                {metric.change}
              </div>
            </div>
            <p className="text-xs text-gray-500 mt-2">{metric.period}</p>
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trend */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Sales & Revenue Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={analyticsData.salesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area type="monotone" dataKey="revenue" stackId="1" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6} />
              <Area type="monotone" dataKey="profit" stackId="1" stroke="#10B981" fill="#10B981" fillOpacity={0.6} />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Customer Sources */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Sources</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={analyticsData.customerSources}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, value }) => `${name}: ${value}%`}
              >
                {customerSources.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Inventory Analysis */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Inventory Turnover by Category</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={analyticsData.inventoryTurnover}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="category" />
            <YAxis />
            <Tooltip />
            <Bar dataKey="turnover" fill="#3B82F6" name="Turnover Rate" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Top Performers */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Top Sales Performers</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Salesperson
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sales Count
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Avg Sale
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {analyticsData.topPerformers.map((performer, index) => (
                <tr key={performer.name}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                          <span className="text-sm font-medium text-white">
                            {performer.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{performer.name}</div>
                        <div className="text-sm text-gray-500">Rank #{index + 1}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {performer.sales}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${performer.revenue.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${Math.round(performer.revenue / performer.sales).toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
