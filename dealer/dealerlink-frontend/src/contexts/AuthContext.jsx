import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI, setAuthToken, getAuthToken, setUser as setStoredUser, getUser } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Check if user is already logged in
  useEffect(() => {
    const token = getAuthToken();
    const storedUser = getUser();

    if (token && storedUser) {
      setUser(storedUser);
      setIsAuthenticated(true);
    }
    setLoading(false);
  }, []);

  const login = async (email, password) => {
    setLoading(true);
    try {
      const response = await authAPI.login({ email, password });
      const { user: userData, token } = response.data;

      setAuthToken(token);
      setStoredUser(userData);
      setUser(userData);
      setIsAuthenticated(true);
      setLoading(false);

      return { success: true };
    } catch (error) {
      setLoading(false);
      const errorMessage = error.response?.data?.non_field_errors?.[0] ||
                          error.response?.data?.detail ||
                          'Login failed';
      return { success: false, error: errorMessage };
    }
  };

  const register = async (userData) => {
    setLoading(true);
    try {
      const response = await authAPI.register(userData);
      const { user: newUser, token } = response.data;

      setAuthToken(token);
      setStoredUser(newUser);
      setUser(newUser);
      setIsAuthenticated(true);
      setLoading(false);

      return { success: true };
    } catch (error) {
      setLoading(false);
      const errorMessage = error.response?.data?.email?.[0] ||
                          error.response?.data?.username?.[0] ||
                          error.response?.data?.non_field_errors?.[0] ||
                          'Registration failed';
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setAuthToken(null);
      setStoredUser(null);
      setUser(null);
      setIsAuthenticated(false);
    }
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
