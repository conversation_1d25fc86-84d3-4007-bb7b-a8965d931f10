@import '@fontsource/inter';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Design System Variables */
:root {
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  --gradient-uber: linear-gradient(135deg, #000000 0%, #434343 100%);
  --shadow-soft: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes float-particles {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-10px) translateX(5px);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) translateX(-5px);
    opacity: 0.7;
  }
  75% {
    transform: translateY(-10px) translateX(3px);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Floating Car Animation */
.floating-car {
  animation: float 6s ease-in-out infinite;
}

.car-shadow {
  animation: pulse-slow 6s ease-in-out infinite;
}

/* Wheel Rotation */
.wheel-left, .wheel-right {
  transform-origin: center;
  animation: rotate 2s linear infinite;
}

.spokes-left, .spokes-right {
  transform-origin: center;
  animation: rotate 2s linear infinite;
}

/* Floating Particles */
.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(59, 130, 246, 0.6);
  border-radius: 50%;
  animation: float-particles 8s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  background: rgba(147, 197, 253, 0.6);
}

.particle-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
  background: rgba(96, 165, 250, 0.6);
}

.particle-4 {
  top: 40%;
  right: 30%;
  animation-delay: 6s;
  background: rgba(59, 130, 246, 0.4);
}

/* Animation Classes */
.animate-slide-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-fade-scale {
  animation: fadeInScale 0.8s ease-out forwards;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

@layer base {
  body {
    @apply bg-white text-gray-900 font-sans;
  }
}

/* Glass Morphism Effects */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern Component System */
@layer components {
  /* Modern Cards */
  .card {
    @apply bg-white rounded-2xl shadow-lg border-0 transition-all duration-300 overflow-hidden;
    box-shadow: var(--shadow-soft);
  }

  .card-hover {
    @apply hover:shadow-2xl hover:-translate-y-2 hover:scale-[1.02];
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-gradient {
    background: var(--gradient-primary);
    @apply text-white;
  }

  .card-glass {
    @apply glass backdrop-blur-xl;
  }

  .card-uber {
    background: var(--gradient-uber);
    @apply text-white;
  }

  /* Modern Buttons */
  .btn-primary {
    @apply text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105;
    background: var(--gradient-primary);
  }

  .btn-secondary {
    @apply bg-white text-gray-700 px-8 py-4 rounded-2xl font-semibold border border-gray-200 hover:bg-gray-50 transition-all duration-300 shadow-md hover:shadow-lg hover:scale-105;
  }

  .btn-gradient {
    @apply text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105;
    background: var(--gradient-secondary);
  }

  .btn-success {
    @apply text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105;
    background: var(--gradient-success);
  }

  .btn-uber {
    @apply text-white px-8 py-4 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105;
    background: var(--gradient-uber);
  }

  /* Modern Input Fields */
  .input-field {
    @apply w-full px-6 py-4 border-2 border-gray-200 rounded-2xl focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 bg-white shadow-sm;
  }

  .input-field:focus {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }

  /* Sidebar Links */
  .sidebar-link {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:text-primary-600 rounded-xl transition-all duration-300 group;
  }

  .sidebar-link.active {
    @apply text-white rounded-xl;
    background: var(--gradient-primary);
  }

  .sidebar-link:hover {
    transform: translateX(4px);
  }
}

/* Hero Sections */
.hero-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  position: relative;
  overflow: hidden;
}

.hero-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: float 20s ease-in-out infinite;
}

.hero-uber {
  background: linear-gradient(135deg, #000000 0%, #434343 100%);
  position: relative;
  overflow: hidden;
}

.hero-uber::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
  animation: float 15s ease-in-out infinite reverse;
}

/* Text Gradients */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-uber {
  background: var(--gradient-uber);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Shadows */
.shadow-soft {
  box-shadow: var(--shadow-soft);
}

.shadow-medium {
  box-shadow: var(--shadow-medium);
}

.shadow-large {
  box-shadow: var(--shadow-large);
}

.shadow-xl-custom {
  box-shadow: var(--shadow-xl);
}

/* Hover Effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-large);
}

/* Status Indicators */
.status-online {
  @apply relative;
}

.status-online::after {
  content: '';
  @apply absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white;
  animation: pulse-soft 2s infinite;
}

/* Loading Skeleton */
.skeleton {
  @apply bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse rounded-2xl;
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Pulse Animation */
@keyframes pulse-soft {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

.pulse-soft {
  animation: pulse-soft 3s ease-in-out infinite;
}

/* Floating Particles */
.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  pointer-events: none;
}

.particle-1 {
  width: 20px;
  height: 20px;
  top: 20%;
  left: 10%;
  animation: float 6s ease-in-out infinite;
}

.particle-2 {
  width: 15px;
  height: 15px;
  top: 60%;
  right: 15%;
  animation: float 8s ease-in-out infinite reverse;
}

.particle-3 {
  width: 25px;
  height: 25px;
  bottom: 30%;
  left: 20%;
  animation: float 10s ease-in-out infinite;
}

.particle-4 {
  width: 12px;
  height: 12px;
  top: 40%;
  right: 30%;
  animation: float 7s ease-in-out infinite reverse;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .card {
    @apply rounded-xl;
  }

  .btn-primary, .btn-secondary, .btn-gradient, .btn-success, .btn-uber {
    @apply px-6 py-3 rounded-xl text-sm;
  }

  .input-field {
    @apply px-4 py-3 rounded-xl;
  }

  .hero-gradient, .hero-uber {
    @apply px-4 py-8;
  }

  .particle {
    display: none;
  }
}
