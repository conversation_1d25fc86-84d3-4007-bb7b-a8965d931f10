@import '@fontsource/inter';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes float-particles {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-10px) translateX(5px);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) translateX(-5px);
    opacity: 0.7;
  }
  75% {
    transform: translateY(-10px) translateX(3px);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Floating Car Animation */
.floating-car {
  animation: float 6s ease-in-out infinite;
}

.car-shadow {
  animation: pulse-slow 6s ease-in-out infinite;
}

/* Wheel Rotation */
.wheel-left, .wheel-right {
  transform-origin: center;
  animation: rotate 2s linear infinite;
}

.spokes-left, .spokes-right {
  transform-origin: center;
  animation: rotate 2s linear infinite;
}

/* Floating Particles */
.particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(59, 130, 246, 0.6);
  border-radius: 50%;
  animation: float-particles 8s ease-in-out infinite;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  top: 60%;
  right: 15%;
  animation-delay: 2s;
  background: rgba(147, 197, 253, 0.6);
}

.particle-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
  background: rgba(96, 165, 250, 0.6);
}

.particle-4 {
  top: 40%;
  right: 30%;
  animation-delay: 6s;
  background: rgba(59, 130, 246, 0.4);
}

/* Animation Classes */
.animate-slide-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-fade-scale {
  animation: fadeInScale 0.8s ease-out forwards;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

@layer base {
  body {
    @apply bg-white text-gray-900 font-sans;
  }
}

@layer components {
  .btn-primary {
    @apply inline-flex items-center px-6 py-3 bg-primary-600 text-white font-semibold rounded-lg hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply inline-flex items-center px-6 py-3 bg-white text-gray-700 font-semibold rounded-lg border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200;
  }

  .input-field {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden;
  }

  .card-hover {
    @apply hover:scale-105 hover:shadow-xl transition-all duration-300;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .sidebar-link {
    @apply flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-primary-600 rounded-lg transition-colors duration-200;
  }

  .sidebar-link.active {
    @apply bg-primary-50 text-primary-600 border-r-2 border-primary-600;
  }
}
