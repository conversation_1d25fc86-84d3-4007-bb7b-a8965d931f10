import React from 'react';

const AnimatedCar = () => {
  return (
    <div className="relative">
      {/* Car SVG with floating animation */}
      <div className="floating-car">
        <svg 
          viewBox="0 0 400 200" 
          className="w-full h-auto max-w-lg mx-auto"
          fill="none" 
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Car Shadow */}
          <ellipse cx="200" cy="180" rx="120" ry="10" fill="rgba(0,0,0,0.1)" className="car-shadow" />
          
          {/* Car Body */}
          <path 
            d="M80 120 L100 100 L140 95 L180 90 L220 90 L260 95 L300 100 L320 120 L320 140 L310 150 L90 150 L80 140 Z" 
            fill="#ffffff" 
            stroke="#e5e7eb" 
            strokeWidth="2"
          />
          
          {/* Car Windows */}
          <path 
            d="M110 120 L130 105 L170 102 L210 102 L250 105 L270 120 L270 130 L110 130 Z" 
            fill="#3b82f6" 
            opacity="0.8"
          />
          
          {/* Car Details */}
          <rect x="85" y="135" width="15" height="8" rx="2" fill="#374151" />
          <rect x="300" y="135" width="15" height="8" rx="2" fill="#374151" />
          
          {/* Headlights */}
          <circle cx="95" cy="125" r="8" fill="#fbbf24" opacity="0.9" />
          <circle cx="305" cy="125" r="8" fill="#ef4444" opacity="0.9" />
          
          {/* Wheels */}
          <circle cx="130" cy="150" r="20" fill="#374151" className="wheel-left" />
          <circle cx="130" cy="150" r="12" fill="#6b7280" />
          <circle cx="130" cy="150" r="6" fill="#9ca3af" />
          
          <circle cx="270" cy="150" r="20" fill="#374151" className="wheel-right" />
          <circle cx="270" cy="150" r="12" fill="#6b7280" />
          <circle cx="270" cy="150" r="6" fill="#9ca3af" />
          
          {/* Wheel Spokes */}
          <g className="spokes-left">
            <line x1="125" y1="150" x2="135" y2="150" stroke="#d1d5db" strokeWidth="2" />
            <line x1="130" y1="145" x2="130" y2="155" stroke="#d1d5db" strokeWidth="2" />
          </g>
          <g className="spokes-right">
            <line x1="265" y1="150" x2="275" y2="150" stroke="#d1d5db" strokeWidth="2" />
            <line x1="270" y1="145" x2="270" y2="155" stroke="#d1d5db" strokeWidth="2" />
          </g>
          
          {/* CarConnect Logo on Car */}
          <text x="200" y="125" textAnchor="middle" fill="#3b82f6" fontSize="12" fontWeight="bold">
            CarConnect
          </text>
        </svg>
      </div>
      
      {/* Floating particles */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="particle particle-1"></div>
        <div className="particle particle-2"></div>
        <div className="particle particle-3"></div>
        <div className="particle particle-4"></div>
      </div>
    </div>
  );
};

export default AnimatedCar;
