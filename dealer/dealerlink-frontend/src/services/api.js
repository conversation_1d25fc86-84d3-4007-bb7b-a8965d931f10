import axios from 'axios';

const API_BASE_URL = 'http://127.0.0.1:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Token ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/auth/register/', userData),
  login: (credentials) => api.post('/auth/login/', credentials),
  logout: () => api.post('/auth/logout/'),
  getProfile: () => api.get('/auth/profile/'),
  updateProfile: (userData) => api.put('/auth/profile/update/', userData),
};

// Cars API
export const carsAPI = {
  // Get all cars with optional filters
  getCars: (params = {}) => api.get('/cars/', { params }),
  
  // Get car details
  getCarDetail: (id) => api.get(`/cars/${id}/`),
  
  // Create new car listing
  createCar: (carData) => {
    const formData = new FormData();
    
    // Add car data
    Object.keys(carData).forEach(key => {
      if (key === 'images') {
        carData.images.forEach(image => {
          formData.append('images', image);
        });
      } else {
        formData.append(key, carData[key]);
      }
    });
    
    return api.post('/cars/create/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // Get user's listings
  getMyListings: () => api.get('/cars/my-listings/'),
  
  // Save/unsave car
  saveCar: (carId) => api.post(`/cars/${carId}/save/`),
  unsaveCar: (carId) => api.delete(`/cars/${carId}/save/`),
  
  // Get saved cars
  getSavedCars: () => api.get('/cars/saved/'),
  
  // Messages
  getMessages: () => api.get('/cars/messages/'),
  sendMessage: (messageData) => api.post('/cars/messages/send/', messageData),
};

// Helper functions
export const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('token', token);
  } else {
    localStorage.removeItem('token');
  }
};

export const getAuthToken = () => {
  return localStorage.getItem('token');
};

export const setUser = (user) => {
  if (user) {
    localStorage.setItem('user', JSON.stringify(user));
  } else {
    localStorage.removeItem('user');
  }
};

export const getUser = () => {
  const user = localStorage.getItem('user');
  return user ? JSON.parse(user) : null;
};

export default api;
