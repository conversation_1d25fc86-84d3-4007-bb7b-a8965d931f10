from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from cars.models import Car, CarImage
import random

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate database with sample car data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample users and cars...')
        
        # Create sample users
        users_data = [
            {'username': 'john_doe', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': 'Do<PERSON>', 'city': 'Chicago', 'state': 'IL', 'zip_code': '60601'},
            {'username': 'sarah_smith', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': 'Smith', 'city': 'Milwaukee', 'state': 'WI', 'zip_code': '53201'},
            {'username': 'mike_johnson', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': '<PERSON>', 'city': 'Detroit', 'state': '<PERSON>', 'zip_code': '48201'},
            {'username': 'lisa_brown', 'email': '<EMAIL>', 'first_name': '<PERSON>', 'last_name': 'Brown', 'city': 'Indianapolis', 'state': 'IN', 'zip_code': '46201'},
        ]
        
        users = []
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={**user_data, 'password': 'password123'}
            )
            if created:
                user.set_password('password123')
                user.save()
            users.append(user)
        
        # Sample car data
        cars_data = [
            {
                'make': 'Honda', 'model': 'Civic', 'year': 2022, 'mileage': 15000,
                'condition': 'excellent', 'transmission': 'automatic', 'fuel_type': 'gasoline',
                'exterior_color': 'Silver', 'price': 24500, 'title': '2022 Honda Civic - Low Miles, Excellent Condition',
                'description': 'Beautiful Honda Civic with low miles. Well maintained, single owner. Perfect for daily commuting.',
                'views': 45, 'inquiries': 8
            },
            {
                'make': 'Toyota', 'model': 'Camry', 'year': 2021, 'mileage': 22000,
                'condition': 'good', 'transmission': 'automatic', 'fuel_type': 'gasoline',
                'exterior_color': 'White', 'price': 26000, 'title': '2021 Toyota Camry - Reliable and Fuel Efficient',
                'description': 'Reliable Toyota Camry with great fuel economy. Perfect family car with spacious interior.',
                'views': 32, 'inquiries': 5
            },
            {
                'make': 'Ford', 'model': 'Mustang', 'year': 2023, 'mileage': 8000,
                'condition': 'excellent', 'transmission': 'manual', 'fuel_type': 'gasoline',
                'exterior_color': 'Red', 'price': 35000, 'title': '2023 Ford Mustang - Sports Car Enthusiast Dream',
                'description': 'Stunning Ford Mustang with manual transmission. Perfect for weekend drives and car enthusiasts.',
                'views': 78, 'inquiries': 15
            },
            {
                'make': 'BMW', 'model': 'X3', 'year': 2020, 'mileage': 35000,
                'condition': 'good', 'transmission': 'automatic', 'fuel_type': 'gasoline',
                'exterior_color': 'Black', 'price': 38000, 'title': '2020 BMW X3 - Luxury SUV with Premium Features',
                'description': 'Luxury BMW X3 with premium features and excellent performance. Well maintained with service records.',
                'views': 56, 'inquiries': 12
            },
            {
                'make': 'Tesla', 'model': 'Model 3', 'year': 2022, 'mileage': 12000,
                'condition': 'excellent', 'transmission': 'automatic', 'fuel_type': 'electric',
                'exterior_color': 'Blue', 'price': 42000, 'title': '2022 Tesla Model 3 - Electric Future is Here',
                'description': 'Amazing Tesla Model 3 with autopilot features. Zero emissions and incredible technology.',
                'views': 89, 'inquiries': 20
            },
            {
                'make': 'Chevrolet', 'model': 'Silverado', 'year': 2021, 'mileage': 28000,
                'condition': 'good', 'transmission': 'automatic', 'fuel_type': 'gasoline',
                'exterior_color': 'Gray', 'price': 32000, 'title': '2021 Chevrolet Silverado - Powerful Work Truck',
                'description': 'Reliable Chevrolet Silverado perfect for work and hauling. Great condition with towing package.',
                'views': 41, 'inquiries': 7
            }
        ]
        
        # Create cars
        for i, car_data in enumerate(cars_data):
            seller = users[i % len(users)]
            car, created = Car.objects.get_or_create(
                seller=seller,
                make=car_data['make'],
                model=car_data['model'],
                year=car_data['year'],
                defaults=car_data
            )
            
            if created:
                self.stdout.write(f'Created car: {car.title}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {len(users)} users and {len(cars_data)} cars'
            )
        )
