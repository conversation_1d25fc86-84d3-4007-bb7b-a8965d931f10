from django.urls import path
from . import views

urlpatterns = [
    path('', views.CarListView.as_view(), name='car_list'),
    path('<int:pk>/', views.CarDetailView.as_view(), name='car_detail'),
    path('create/', views.CarCreateView.as_view(), name='car_create'),
    path('my-listings/', views.MyListingsView.as_view(), name='my_listings'),
    path('<int:car_id>/save/', views.toggle_saved_car, name='toggle_saved_car'),
    path('saved/', views.SavedCarsView.as_view(), name='saved_cars'),
    path('messages/', views.MessagesView.as_view(), name='messages'),
    path('messages/send/', views.SendMessageView.as_view(), name='send_message'),
]
