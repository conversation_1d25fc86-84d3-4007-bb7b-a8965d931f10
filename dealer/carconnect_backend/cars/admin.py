from django.contrib import admin
from .models import Car, CarImage, SavedCar, Message


class CarImageInline(admin.TabularInline):
    model = CarImage
    extra = 1


@admin.register(Car)
class CarAdmin(admin.ModelAdmin):
    list_display = ('title', 'make', 'model', 'year', 'price', 'seller', 'status', 'views', 'created_at')
    list_filter = ('make', 'status', 'year', 'condition', 'fuel_type')
    search_fields = ('title', 'make', 'model', 'seller__username')
    readonly_fields = ('views', 'inquiries', 'created_at', 'updated_at')
    inlines = [CarImageInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('seller', 'make', 'model', 'year', 'mileage', 'condition')
        }),
        ('Details', {
            'fields': ('vin', 'transmission', 'fuel_type', 'exterior_color', 'interior_color')
        }),
        ('Listing', {
            'fields': ('title', 'description', 'price', 'status')
        }),
        ('Metrics', {
            'fields': ('views', 'inquiries'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(SavedCar)
class SavedCarAdmin(admin.ModelAdmin):
    list_display = ('user', 'car', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'car__title')


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ('sender', 'recipient', 'car', 'is_read', 'created_at')
    list_filter = ('is_read', 'created_at')
    search_fields = ('sender__username', 'recipient__username', 'car__title')
    readonly_fields = ('created_at',)
