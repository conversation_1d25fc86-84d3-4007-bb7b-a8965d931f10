from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db.models import Q
from .models import Car, SavedCar, Message
from .serializers import (
    CarListSerializer, CarDetailSerializer, CarCreateSerializer,
    SavedCarSerializer, MessageSerializer, MessageCreateSerializer
)


class CarListView(generics.ListAPIView):
    serializer_class = CarListSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get_queryset(self):
        queryset = Car.objects.filter(status='active')
        search = self.request.query_params.get('search', None)
        make = self.request.query_params.get('make', None)
        min_price = self.request.query_params.get('min_price', None)
        max_price = self.request.query_params.get('max_price', None)

        if search:
            queryset = queryset.filter(
                Q(make__icontains=search) |
                Q(model__icontains=search) |
                Q(title__icontains=search)
            )
        if make:
            queryset = queryset.filter(make__iexact=make)
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)

        return queryset


class CarDetailView(generics.RetrieveAPIView):
    queryset = Car.objects.all()
    serializer_class = CarDetailSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]

    def get(self, request, *args, **kwargs):
        car = self.get_object()
        # Increment view count
        car.views += 1
        car.save(update_fields=['views'])
        return super().get(request, *args, **kwargs)


class CarCreateView(generics.CreateAPIView):
    serializer_class = CarCreateSerializer
    permission_classes = [IsAuthenticated]


class MyListingsView(generics.ListAPIView):
    serializer_class = CarListSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Car.objects.filter(seller=self.request.user)


@api_view(['POST', 'DELETE'])
@permission_classes([IsAuthenticated])
def toggle_saved_car(request, car_id):
    car = get_object_or_404(Car, id=car_id)

    if request.method == 'POST':
        saved_car, created = SavedCar.objects.get_or_create(
            user=request.user,
            car=car
        )
        if created:
            return Response({'message': 'Car saved successfully'})
        else:
            return Response({'message': 'Car already saved'}, status=status.HTTP_400_BAD_REQUEST)

    elif request.method == 'DELETE':
        try:
            saved_car = SavedCar.objects.get(user=request.user, car=car)
            saved_car.delete()
            return Response({'message': 'Car removed from saved'})
        except SavedCar.DoesNotExist:
            return Response({'message': 'Car not in saved list'}, status=status.HTTP_400_BAD_REQUEST)


class SavedCarsView(generics.ListAPIView):
    serializer_class = SavedCarSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return SavedCar.objects.filter(user=self.request.user)


class MessagesView(generics.ListAPIView):
    serializer_class = MessageSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Message.objects.filter(
            Q(sender=self.request.user) | Q(recipient=self.request.user)
        ).distinct()


class SendMessageView(generics.CreateAPIView):
    serializer_class = MessageCreateSerializer
    permission_classes = [IsAuthenticated]
