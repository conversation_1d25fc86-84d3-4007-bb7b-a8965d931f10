# Generated by Django 5.2.4 on 2025-07-12 02:26

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Car",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("make", models.Char<PERSON>ield(max_length=50)),
                ("model", models.CharField(max_length=50)),
                ("year", models.IntegerField()),
                ("mileage", models.IntegerField()),
                (
                    "condition",
                    models.CharField(
                        choices=[
                            ("excellent", "Excellent"),
                            ("good", "Good"),
                            ("fair", "Fair"),
                            ("poor", "Poor"),
                        ],
                        max_length=20,
                    ),
                ),
                ("vin", models.CharField(blank=True, max_length=17)),
                (
                    "transmission",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("automatic", "Automatic"),
                            ("manual", "Manual"),
                            ("cvt", "CVT"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "fuel_type",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("gasoline", "Gasoline"),
                            ("diesel", "Diesel"),
                            ("hybrid", "Hybrid"),
                            ("electric", "Electric"),
                        ],
                        max_length=20,
                    ),
                ),
                ("exterior_color", models.CharField(blank=True, max_length=30)),
                ("interior_color", models.CharField(blank=True, max_length=30)),
                ("price", models.DecimalField(decimal_places=2, max_digits=10)),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("pending", "Pending"),
                            ("sold", "Sold"),
                            ("inactive", "Inactive"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("views", models.IntegerField(default=0)),
                ("inquiries", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CarImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image", models.ImageField(upload_to="cars/")),
                ("is_primary", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-is_primary", "created_at"],
            },
        ),
        migrations.CreateModel(
            name="Message",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                ("is_read", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SavedCar",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
