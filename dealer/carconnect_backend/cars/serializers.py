from rest_framework import serializers
from .models import Car, CarImage, SavedCar, Message
from users.serializers import UserProfileSerializer


class CarImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = CarImage
        fields = ['id', 'image', 'is_primary']


class CarListSerializer(serializers.ModelSerializer):
    seller = UserProfileSerializer(read_only=True)
    images = CarImageSerializer(many=True, read_only=True)
    is_saved = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()

    class Meta:
        model = Car
        fields = ['id', 'make', 'model', 'year', 'mileage', 'condition', 
                 'price', 'title', 'status', 'views', 'seller', 'images', 
                 'is_saved', 'distance', 'created_at']

    def get_is_saved(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return SavedCar.objects.filter(user=request.user, car=obj).exists()
        return False

    def get_distance(self, obj):
        # TODO: Calculate actual distance based on user location
        return "2.3 miles away"


class CarDetailSerializer(serializers.ModelSerializer):
    seller = UserProfileSerializer(read_only=True)
    images = CarImageSerializer(many=True, read_only=True)
    is_saved = serializers.SerializerMethodField()
    distance = serializers.SerializerMethodField()

    class Meta:
        model = Car
        fields = ['id', 'make', 'model', 'year', 'mileage', 'condition', 
                 'vin', 'transmission', 'fuel_type', 'exterior_color', 
                 'interior_color', 'price', 'title', 'description', 
                 'status', 'views', 'inquiries', 'seller', 'images', 
                 'is_saved', 'distance', 'created_at', 'updated_at']

    def get_is_saved(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return SavedCar.objects.filter(user=request.user, car=obj).exists()
        return False

    def get_distance(self, obj):
        # TODO: Calculate actual distance based on user location
        return "2.3 miles away"


class CarCreateSerializer(serializers.ModelSerializer):
    images = serializers.ListField(
        child=serializers.ImageField(),
        write_only=True,
        required=False
    )

    class Meta:
        model = Car
        fields = ['make', 'model', 'year', 'mileage', 'condition', 
                 'vin', 'transmission', 'fuel_type', 'exterior_color', 
                 'interior_color', 'price', 'title', 'description', 'images']

    def create(self, validated_data):
        images_data = validated_data.pop('images', [])
        car = Car.objects.create(seller=self.context['request'].user, **validated_data)
        
        for i, image_data in enumerate(images_data):
            CarImage.objects.create(
                car=car,
                image=image_data,
                is_primary=(i == 0)
            )
        
        return car


class SavedCarSerializer(serializers.ModelSerializer):
    car = CarListSerializer(read_only=True)

    class Meta:
        model = SavedCar
        fields = ['id', 'car', 'created_at']


class MessageSerializer(serializers.ModelSerializer):
    sender = UserProfileSerializer(read_only=True)
    recipient = UserProfileSerializer(read_only=True)
    car = CarListSerializer(read_only=True)

    class Meta:
        model = Message
        fields = ['id', 'sender', 'recipient', 'car', 'content', 
                 'is_read', 'created_at']


class MessageCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Message
        fields = ['recipient', 'car', 'content']

    def create(self, validated_data):
        return Message.objects.create(
            sender=self.context['request'].user,
            **validated_data
        )
