{% extends "rest_framework/base.html" %}
{% load rest_framework %}

{% block body %}
<body class="container">
  <div class="container-fluid" style="margin-top: 30px">
    <div class="row-fluid">
      <div class="well" style="width: 320px; margin-left: auto; margin-right: auto">
        <div class="row-fluid">
          <div>
            {% block branding %}<h3 style="margin: 0 0 20px;">Django REST framework</h3>{% endblock %}
          </div>
        </div><!-- /row fluid -->

        <div class="row-fluid">
          <div>
            <form action="{% url 'rest_framework:login' %}" role="form" method="post">
              {% csrf_token %}
              <input type="hidden" name="next" value="{{ next }}" />

              <div id="div_id_username" class="clearfix control-group {% if form.username.errors %}error{% endif %}">
                <div class="form-group">
                  <label for="id_username">{{ form.username.label }}:</label>
                  <input type="text" name="username" maxlength="100"
                      autocapitalize="off"
                      autocorrect="off" class="form-control textinput textInput"
                      id="id_username" required autofocus
                      {% if form.username.value %}value="{{ form.username.value }}"{% endif %}>
                  {% if form.username.errors %}
                    <p class="text-error">
                      {{ form.username.errors|striptags }}
                    </p>
                  {% endif %}
                </div>
              </div>

              <div id="div_id_password" class="clearfix control-group {% if form.password.errors %}error{% endif %}">
                <div class="form-group">
                  <label for="id_password">{{ form.password.label }}:</label>
                  <input type="password" name="password" maxlength="100" autocapitalize="off" autocorrect="off" class="form-control textinput textInput" id="id_password" required>
                  {% if form.password.errors %}
                    <p class="text-error">
                      {{ form.password.errors|striptags }}
                    </p>
                  {% endif %}
                </div>
              </div>

              {% if form.non_field_errors %}
                {% for error in form.non_field_errors %}
                  <div class="well well-small text-error" style="border: none">{{ error }}</div>
                {% endfor %}
              {% endif %}

              <div class="form-actions-no-box">
                <input type="submit" name="submit" value="Log in" class="btn btn-primary form-control" id="submit-id-submit">
              </div>
            </form>
          </div>
        </div><!-- /.row-fluid -->
      </div><!--/.well-->
    </div><!-- /.row-fluid -->
  </div><!-- /.container-fluid -->
</body>
{% endblock %}
